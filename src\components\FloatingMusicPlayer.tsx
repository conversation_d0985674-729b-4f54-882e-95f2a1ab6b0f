"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  PlayIcon,
  PauseIcon,
  TrackNextIcon,
  TrackPreviousIcon,
} from "@radix-ui/react-icons";
import { useMusicPlayer } from "@/contexts/MusicPlayerContext";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import MusicPlayer from "./MusicPlayer";

export default function FloatingMusicPlayer() {
  const {
    currentTrack,
    isPlaying,
    isLoading,
    togglePlayPause,
    handleNext,
    handlePrevious,
  } = useMusicPlayer();

  // Only show if there's a current track and it has been interacted with
  if (!currentTrack || (!isPlaying && !isLoading)) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 100 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 100 }}
        transition={{
          duration: 0.3,
          ease: "easeOut",
          type: "spring",
          stiffness: 400,
          damping: 25,
        }}
        className="fixed top-2 left-4 z-40"
        style={{
          willChange: "transform, opacity",
          transform: "translateZ(0)",
        }}
      >
        <Card className="p-3 bg-background/95 backdrop-blur-sm border shadow-lg max-w-[280px]">
          <div className="flex items-center gap-3">
            {/* Track Info */}
            <Dialog>
              <DialogTrigger asChild>
                <div className="flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity flex-1 min-w-0">
                  <div className="w-10 h-10 bg-muted rounded flex items-center justify-center overflow-hidden flex-shrink-0">
                    {currentTrack.cover ? (
                      <img
                        src={currentTrack.cover}
                        alt={currentTrack.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-6 h-6 bg-primary/20 rounded" />
                    )}
                  </div>
                  <div className="text-xs min-w-0 flex-1">
                    <p className="font-medium truncate">{currentTrack.title}</p>
                    <p className="text-muted-foreground truncate">
                      {currentTrack.artist}
                    </p>
                  </div>
                </div>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Music Player</DialogTitle>
                </DialogHeader>
                <MusicPlayer />
              </DialogContent>
            </Dialog>

            {/* Controls */}
            <div className="flex items-center gap-1 flex-shrink-0">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handlePrevious}
                  className="h-7 w-7"
                  style={{ willChange: "transform" }}
                >
                  <TrackPreviousIcon className="h-3 w-3" />
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="default"
                  size="icon"
                  onClick={togglePlayPause}
                  disabled={isLoading}
                  className="h-8 w-8"
                  style={{ willChange: "transform" }}
                >
                  {isLoading ? (
                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : isPlaying ? (
                    <PauseIcon className="h-3 w-3" />
                  ) : (
                    <PlayIcon className="h-3 w-3" />
                  )}
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleNext}
                  className="h-7 w-7"
                  style={{ willChange: "transform" }}
                >
                  <TrackNextIcon className="h-3 w-3" />
                </Button>
              </motion.div>
            </div>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
